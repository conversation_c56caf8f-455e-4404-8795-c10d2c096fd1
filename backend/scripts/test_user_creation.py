#!/usr/bin/env python3
"""
测试用户创建功能
验证默认用户是否正确创建到数据库中
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.config.settings import get_settings
from shared.database.connection import get_database
from shared.database.models.user import UserORM
from shared.models.user import UserRole
from sqlalchemy import select
from passlib.context import CryptContext

# 配置
settings = get_settings()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def test_user_creation():
    """测试用户创建功能"""
    print("🧪 测试用户创建功能...")
    
    try:
        # 获取数据库连接
        database = await get_database()
        
        async with database.get_async_session() as session:
            # 查询所有用户
            stmt = select(UserORM)
            result = await session.execute(stmt)
            users = result.scalars().all()
            
            print(f"\n📊 数据库中共有 {len(users)} 个用户:")
            
            for user in users:
                print(f"  👤 用户: {user.username}")
                print(f"     📧 邮箱: {user.email}")
                print(f"     🔑 角色: {user.role.value}")
                print(f"     ✅ 状态: {user.status}")
                print(f"     🔐 已验证: {user.is_verified}")
                print(f"     📅 创建时间: {user.created_at}")
                print(f"     📝 备注: {user.notes}")
                print()
            
            # 测试密码验证
            print("🔐 测试密码验证:")
            
            # 查找admin用户
            admin_stmt = select(UserORM).where(UserORM.username == "admin")
            admin_result = await session.execute(admin_stmt)
            admin_user = admin_result.scalar_one_or_none()
            
            if admin_user:
                # 测试正确密码
                correct_password = "admin123456"
                is_valid = pwd_context.verify(correct_password, admin_user.password_hash)
                print(f"  ✅ admin用户密码 '{correct_password}' 验证: {'通过' if is_valid else '失败'}")
                
                # 测试错误密码
                wrong_password = "wrongpassword"
                is_valid = pwd_context.verify(wrong_password, admin_user.password_hash)
                print(f"  ❌ admin用户密码 '{wrong_password}' 验证: {'通过' if is_valid else '失败'}")
            else:
                print("  ⚠️ 未找到admin用户")
            
            # 查找demo_user用户
            demo_stmt = select(UserORM).where(UserORM.username == "demo_user")
            demo_result = await session.execute(demo_stmt)
            demo_user = demo_result.scalar_one_or_none()
            
            if demo_user:
                # 测试正确密码
                correct_password = "demo123456"
                is_valid = pwd_context.verify(correct_password, demo_user.password_hash)
                print(f"  ✅ demo_user用户密码 '{correct_password}' 验证: {'通过' if is_valid else '失败'}")
            else:
                print("  ⚠️ 未找到demo_user用户")
        
        print("\n✅ 用户创建功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    print("🚀 开始测试用户创建功能...")
    
    success = await test_user_creation()
    
    if success:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
