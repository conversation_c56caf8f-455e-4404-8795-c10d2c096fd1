#!/usr/bin/env python3
"""
测试用户创建修复
验证外键问题是否已解决
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_user_model_import():
    """测试用户模型导入"""
    print("🧪 测试用户模型导入...")
    
    try:
        # 测试导入用户模型
        from shared.database.models.user import UserORM
        print("✅ UserORM 导入成功")
        
        # 测试导入用户角色
        from shared.models.user import UserRole
        print("✅ UserRole 导入成功")
        
        # 测试数据库连接
        from shared.database.connection import get_database
        print("✅ 数据库连接模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_database_connection():
    """测试数据库连接"""
    print("\n🔗 测试数据库连接...")
    
    try:
        from shared.database.connection import get_database
        
        # 获取数据库实例
        database = await get_database()
        print("✅ 数据库实例获取成功")
        
        # 测试连接
        connection_ok = await database.test_connection()
        if connection_ok:
            print("✅ 数据库连接测试成功")
        else:
            print("❌ 数据库连接测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_user_creation_logic():
    """测试用户创建逻辑"""
    print("\n👤 测试用户创建逻辑...")
    
    try:
        from shared.database.connection import get_database
        from shared.database.models.user import UserORM
        from shared.models.user import UserRole
        from sqlalchemy import text
        from passlib.context import CryptContext
        import uuid
        from datetime import datetime
        
        # 密码加密上下文
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # 获取数据库连接
        database = await get_database()
        
        async with database.get_async_session() as session:
            # 测试用户查询 (使用原生SQL)
            check_sql = text("SELECT id FROM users WHERE username = :username")
            result = await session.execute(check_sql, {"username": "test_user_not_exist"})
            existing_user = result.fetchone()
            
            if existing_user is None:
                print("✅ 用户查询测试成功 (用户不存在)")
            else:
                print("⚠️ 测试用户已存在")
            
            # 测试密码加密
            test_password = "test123456"
            password_hash = pwd_context.hash(test_password)
            print("✅ 密码加密测试成功")
            
            # 测试密码验证
            is_valid = pwd_context.verify(test_password, password_hash)
            if is_valid:
                print("✅ 密码验证测试成功")
            else:
                print("❌ 密码验证测试失败")
                return False
            
            # 测试UUID生成
            user_id = str(uuid.uuid4())
            print(f"✅ UUID生成测试成功: {user_id}")
            
        return True
        
    except Exception as e:
        print(f"❌ 用户创建逻辑测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    print("🚀 开始测试用户创建修复...")
    
    tests = [
        ("模型导入", test_user_model_import),
        ("数据库连接", test_database_connection),
        ("用户创建逻辑", test_user_creation_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if await test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！用户创建修复成功！")
        return 0
    else:
        print("💥 部分测试失败！需要进一步修复！")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
