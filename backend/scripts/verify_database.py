#!/usr/bin/env python3
"""
数据库验证脚本
验证数据库连接和基本功能
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from shared.database.connection import get_database
from shared.vector_db.client import get_qdrant_client
from shared.config.settings import get_settings
import aiomysql

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


async def verify_mysql_connection():
    """验证MySQL连接"""
    logger.info("🔍 验证MySQL连接...")
    
    try:
        # 测试基本连接
        connection = await aiomysql.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            db=settings.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        cursor = await connection.cursor()
        
        # 测试查询
        await cursor.execute("SELECT VERSION()")
        version = await cursor.fetchone()
        logger.info(f"✅ MySQL版本: {version[0]}")
        
        # 检查数据库
        await cursor.execute("SELECT DATABASE()")
        database = await cursor.fetchone()
        logger.info(f"✅ 当前数据库: {database[0]}")
        
        # 检查表
        await cursor.execute("SHOW TABLES")
        tables = await cursor.fetchall()
        logger.info(f"✅ 数据库表数量: {len(tables)}")
        
        if tables:
            logger.info("📋 数据库表:")
            for table in tables:
                logger.info(f"  - {table[0]}")
        
        await cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        return False


async def verify_redis_connection():
    """验证Redis连接"""
    logger.info("🔍 验证Redis连接...")
    
    try:
        import redis.asyncio as redis
        
        # 创建Redis连接
        redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD if hasattr(settings, 'REDIS_PASSWORD') else None,
            decode_responses=True
        )
        
        # 测试连接
        pong = await redis_client.ping()
        if pong:
            logger.info("✅ Redis连接成功")
            
            # 获取Redis信息
            info = await redis_client.info()
            logger.info(f"✅ Redis版本: {info.get('redis_version', 'Unknown')}")
            logger.info(f"✅ 已用内存: {info.get('used_memory_human', 'Unknown')}")
            
            # 测试读写
            await redis_client.set("test_key", "test_value", ex=10)
            value = await redis_client.get("test_key")
            if value == "test_value":
                logger.info("✅ Redis读写测试成功")
                await redis_client.delete("test_key")
            else:
                logger.warning("⚠️ Redis读写测试失败")
        
        await redis_client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        return False


async def verify_qdrant_connection():
    """验证Qdrant连接"""
    logger.info("🔍 验证Qdrant连接...")
    
    try:
        # 获取Qdrant客户端
        qdrant_manager = await get_qdrant_client()
        
        # 检查健康状态
        health = await qdrant_manager.health_check()
        if health["healthy"]:
            logger.info("✅ Qdrant连接成功")
            logger.info(f"✅ Qdrant状态: {health.get('message', 'OK')}")
            
            # 获取集合信息
            collections = await qdrant_manager.list_collections()
            logger.info(f"✅ 向量集合数量: {len(collections)}")
            
            if collections:
                logger.info("📂 向量集合:")
                for collection in collections:
                    logger.info(f"  - {collection}")
            
            return True
        else:
            logger.error(f"❌ Qdrant健康检查失败: {health.get('message', 'Unknown error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Qdrant连接失败: {e}")
        return False


async def verify_database_orm():
    """验证数据库ORM连接"""
    logger.info("🔍 验证数据库ORM连接...")
    
    try:
        # 获取数据库连接
        database = await get_database()
        
        # 测试连接
        if await database.test_connection():
            logger.info("✅ ORM数据库连接成功")
            
            # 测试会话
            async with database.get_async_session() as session:
                from sqlalchemy import text
                result = await session.execute(text("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = :db_name"), {"db_name": settings.MYSQL_DATABASE})
                count = result.scalar()
                logger.info(f"✅ 数据库表数量: {count}")
            
            return True
        else:
            logger.error("❌ ORM数据库连接失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ ORM数据库连接失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🚀 开始验证数据库连接...")
    
    results = {
        "MySQL": False,
        "Redis": False,
        "Qdrant": False,
        "ORM": False
    }
    
    # 验证MySQL
    results["MySQL"] = await verify_mysql_connection()
    
    # 验证Redis
    results["Redis"] = await verify_redis_connection()
    
    # 验证Qdrant
    results["Qdrant"] = await verify_qdrant_connection()
    
    # 验证ORM
    results["ORM"] = await verify_database_orm()
    
    # 显示结果
    logger.info("\n" + "="*50)
    logger.info("📊 验证结果:")
    
    all_success = True
    for service, success in results.items():
        status = "✅" if success else "❌"
        logger.info(f"  {status} {service}")
        if not success:
            all_success = False
    
    logger.info("="*50)
    
    if all_success:
        logger.info("🎉 所有数据库服务验证成功！")
        logger.info("💡 系统已准备就绪，可以启动应用服务")
        return 0
    else:
        logger.error("❌ 部分数据库服务验证失败")
        logger.info("💡 请检查服务状态并重新运行初始化脚本")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n⚠️ 验证被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        sys.exit(1)
